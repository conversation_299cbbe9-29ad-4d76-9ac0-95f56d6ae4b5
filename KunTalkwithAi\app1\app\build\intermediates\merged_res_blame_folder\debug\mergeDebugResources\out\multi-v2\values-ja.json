{"logs": [{"outputFile": "com.example.everytalk.app-mergeDebugResources-53:/values-ja/values-ja.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e5127fbf4aa18facdfc5d332b39b8229\\transformed\\core-1.16.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,247,341,437,530,623,724", "endColumns": "91,99,93,95,92,92,100,100", "endOffsets": "142,242,336,432,525,618,719,820"}, "to": {"startLines": "3,4,5,6,7,8,9,83", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "176,268,368,462,558,651,744,7813", "endColumns": "91,99,93,95,92,92,100,100", "endOffsets": "263,363,457,553,646,739,840,7909"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\39e186d1df9a6e64a80555ea95166efd\\transformed\\material3-release\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,166,273,382,488,581,671,778,892,1000,1124,1206,1303,1388,1478,1585,1698,1800,1924,2046,2160,2287,2397,2498,2602,2710,2796,2891,2999,3111,3202,3299,3396,3517,3643,3742,3834,3909,4002,4094,4195,4278,4361,4458,4538,4620,4718,4813,4906,5003,5086,5182,5277,5375,5486,5566,5663", "endColumns": "110,106,108,105,92,89,106,113,107,123,81,96,84,89,106,112,101,123,121,113,126,109,100,103,107,85,94,107,111,90,96,96,120,125,98,91,74,92,91,100,82,82,96,79,81,97,94,92,96,82,95,94,97,110,79,96,93", "endOffsets": "161,268,377,483,576,666,773,887,995,1119,1201,1298,1383,1473,1580,1693,1795,1919,2041,2155,2282,2392,2493,2597,2705,2791,2886,2994,3106,3197,3294,3391,3512,3638,3737,3829,3904,3997,4089,4190,4273,4356,4453,4533,4615,4713,4808,4901,4998,5081,5177,5272,5370,5481,5561,5658,5752"}, "to": {"startLines": "17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1434,1545,1652,1761,1867,1960,2050,2157,2271,2379,2503,2585,2682,2767,2857,2964,3077,3179,3303,3425,3539,3666,3776,3877,3981,4089,4175,4270,4378,4490,4581,4678,4775,4896,5022,5121,5213,5288,5381,5473,5574,5657,5740,5837,5917,5999,6097,6192,6285,6382,6465,6561,6656,6754,6865,6945,7042", "endColumns": "110,106,108,105,92,89,106,113,107,123,81,96,84,89,106,112,101,123,121,113,126,109,100,103,107,85,94,107,111,90,96,96,120,125,98,91,74,92,91,100,82,82,96,79,81,97,94,92,96,82,95,94,97,110,79,96,93", "endOffsets": "1540,1647,1756,1862,1955,2045,2152,2266,2374,2498,2580,2677,2762,2852,2959,3072,3174,3298,3420,3534,3661,3771,3872,3976,4084,4170,4265,4373,4485,4576,4673,4770,4891,5017,5116,5208,5283,5376,5468,5569,5652,5735,5832,5912,5994,6092,6187,6280,6377,6460,6556,6651,6749,6860,6940,7037,7131"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\78e087dd1cdd09d6432d260deb7bf0a8\\transformed\\foundation-release\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,126,211", "endColumns": "70,84,81", "endOffsets": "121,206,288"}, "to": {"startLines": "2,87,88", "startColumns": "4,4,4", "startOffsets": "105,8172,8257", "endColumns": "70,84,81", "endOffsets": "171,8252,8334"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e5d97d34d84112940f02348422c3753d\\transformed\\ui-release\\res\\values-ja\\values-ja.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "177,263,341,430,527,610,688,766,851,926,1000,1076,1145,1228,1301,1373,1443,1519,1584", "endColumns": "85,77,88,96,82,77,77,84,74,73,75,68,82,72,71,69,75,64,116", "endOffsets": "258,336,425,522,605,683,761,846,921,995,1071,1140,1223,1296,1368,1438,1514,1579,1696"}, "to": {"startLines": "10,11,12,13,14,15,16,74,75,76,77,78,79,80,81,82,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "845,931,1009,1098,1195,1278,1356,7136,7221,7296,7370,7446,7515,7598,7671,7743,7914,7990,8055", "endColumns": "85,77,88,96,82,77,77,84,74,73,75,68,82,72,71,69,75,64,116", "endOffsets": "926,1004,1093,1190,1273,1351,1429,7216,7291,7365,7441,7510,7593,7666,7738,7808,7985,8050,8167"}}]}]}