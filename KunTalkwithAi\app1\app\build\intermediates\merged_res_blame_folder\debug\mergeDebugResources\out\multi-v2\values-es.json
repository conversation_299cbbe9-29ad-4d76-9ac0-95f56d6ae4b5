{"logs": [{"outputFile": "com.example.everytalk.app-mergeDebugResources-53:/values-es/values-es.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\39e186d1df9a6e64a80555ea95166efd\\transformed\\material3-release\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,176,295,418,539,639,739,856,999,1117,1265,1350,1457,1554,1656,1770,1888,2000,2138,2275,2419,2588,2724,2844,2966,3096,3194,3290,3411,3546,3649,3763,3878,4015,4156,4267,4372,4459,4555,4651,4767,4854,4940,5051,5134,5218,5319,5425,5525,5628,5717,5828,5929,6038,6157,6240,6357", "endColumns": "120,118,122,120,99,99,116,142,117,147,84,106,96,101,113,117,111,137,136,143,168,135,119,121,129,97,95,120,134,102,113,114,136,140,110,104,86,95,95,115,86,85,110,82,83,100,105,99,102,88,110,100,108,118,82,116,108", "endOffsets": "171,290,413,534,634,734,851,994,1112,1260,1345,1452,1549,1651,1765,1883,1995,2133,2270,2414,2583,2719,2839,2961,3091,3189,3285,3406,3541,3644,3758,3873,4010,4151,4262,4367,4454,4550,4646,4762,4849,4935,5046,5129,5213,5314,5420,5520,5623,5712,5823,5924,6033,6152,6235,6352,6461"}, "to": {"startLines": "17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1557,1678,1797,1920,2041,2141,2241,2358,2501,2619,2767,2852,2959,3056,3158,3272,3390,3502,3640,3777,3921,4090,4226,4346,4468,4598,4696,4792,4913,5048,5151,5265,5380,5517,5658,5769,5874,5961,6057,6153,6269,6356,6442,6553,6636,6720,6821,6927,7027,7130,7219,7330,7431,7540,7659,7742,7859", "endColumns": "120,118,122,120,99,99,116,142,117,147,84,106,96,101,113,117,111,137,136,143,168,135,119,121,129,97,95,120,134,102,113,114,136,140,110,104,86,95,95,115,86,85,110,82,83,100,105,99,102,88,110,100,108,118,82,116,108", "endOffsets": "1673,1792,1915,2036,2136,2236,2353,2496,2614,2762,2847,2954,3051,3153,3267,3385,3497,3635,3772,3916,4085,4221,4341,4463,4593,4691,4787,4908,5043,5146,5260,5375,5512,5653,5764,5869,5956,6052,6148,6264,6351,6437,6548,6631,6715,6816,6922,7022,7125,7214,7325,7426,7535,7654,7737,7854,7963"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e5127fbf4aa18facdfc5d332b39b8229\\transformed\\core-1.16.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,787", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "149,251,351,449,556,662,782,883"}, "to": {"startLines": "3,4,5,6,7,8,9,83", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "185,284,386,486,584,691,797,8718", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "279,381,481,579,686,792,912,8814"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\78e087dd1cdd09d6432d260deb7bf0a8\\transformed\\foundation-release\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,135,235", "endColumns": "79,99,101", "endOffsets": "130,230,332"}, "to": {"startLines": "2,87,88", "startColumns": "4,4,4", "startOffsets": "105,9092,9192", "endColumns": "79,99,101", "endOffsets": "180,9187,9289"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e5d97d34d84112940f02348422c3753d\\transformed\\ui-release\\res\\values-es\\values-es.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "186,282,364,462,565,654,733,826,918,1005,1091,1182,1259,1344,1420,1500,1576,1658,1728", "endColumns": "95,81,97,102,88,78,92,91,86,85,90,76,84,75,79,75,81,69,120", "endOffsets": "277,359,457,560,649,728,821,913,1000,1086,1177,1254,1339,1415,1495,1571,1653,1723,1844"}, "to": {"startLines": "10,11,12,13,14,15,16,74,75,76,77,78,79,80,81,82,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "917,1013,1095,1193,1296,1385,1464,7968,8060,8147,8233,8324,8401,8486,8562,8642,8819,8901,8971", "endColumns": "95,81,97,102,88,78,92,91,86,85,90,76,84,75,79,75,81,69,120", "endOffsets": "1008,1090,1188,1291,1380,1459,1552,8055,8142,8228,8319,8396,8481,8557,8637,8713,8896,8966,9087"}}]}]}