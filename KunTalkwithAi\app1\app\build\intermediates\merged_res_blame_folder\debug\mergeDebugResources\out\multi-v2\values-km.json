{"logs": [{"outputFile": "com.example.everytalk.app-mergeDebugResources-53:/values-km/values-km.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\39e186d1df9a6e64a80555ea95166efd\\transformed\\material3-release\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,292,398,514,616,722,845,989,1117,1269,1360,1460,1560,1670,1794,1919,2024,2150,2276,2404,2566,2688,2802,2915,3038,3139,3239,3365,3504,3608,3713,3825,3950,4078,4195,4303,4379,4476,4572,4680,4768,4856,4957,5037,5121,5221,5323,5419,5528,5615,5720,5818,5929,6046,6126,6233", "endColumns": "117,118,105,115,101,105,122,143,127,151,90,99,99,109,123,124,104,125,125,127,161,121,113,112,122,100,99,125,138,103,104,111,124,127,116,107,75,96,95,107,87,87,100,79,83,99,101,95,108,86,104,97,110,116,79,106,99", "endOffsets": "168,287,393,509,611,717,840,984,1112,1264,1355,1455,1555,1665,1789,1914,2019,2145,2271,2399,2561,2683,2797,2910,3033,3134,3234,3360,3499,3603,3708,3820,3945,4073,4190,4298,4374,4471,4567,4675,4763,4851,4952,5032,5116,5216,5318,5414,5523,5610,5715,5813,5924,6041,6121,6228,6328"}, "to": {"startLines": "17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1535,1653,1772,1878,1994,2096,2202,2325,2469,2597,2749,2840,2940,3040,3150,3274,3399,3504,3630,3756,3884,4046,4168,4282,4395,4518,4619,4719,4845,4984,5088,5193,5305,5430,5558,5675,5783,5859,5956,6052,6160,6248,6336,6437,6517,6601,6701,6803,6899,7008,7095,7200,7298,7409,7526,7606,7713", "endColumns": "117,118,105,115,101,105,122,143,127,151,90,99,99,109,123,124,104,125,125,127,161,121,113,112,122,100,99,125,138,103,104,111,124,127,116,107,75,96,95,107,87,87,100,79,83,99,101,95,108,86,104,97,110,116,79,106,99", "endOffsets": "1648,1767,1873,1989,2091,2197,2320,2464,2592,2744,2835,2935,3035,3145,3269,3394,3499,3625,3751,3879,4041,4163,4277,4390,4513,4614,4714,4840,4979,5083,5188,5300,5425,5553,5670,5778,5854,5951,6047,6155,6243,6331,6432,6512,6596,6696,6798,6894,7003,7090,7195,7293,7404,7521,7601,7708,7808"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e5127fbf4aa18facdfc5d332b39b8229\\transformed\\core-1.16.0\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,253,351,451,552,664,776", "endColumns": "94,102,97,99,100,111,111,100", "endOffsets": "145,248,346,446,547,659,771,872"}, "to": {"startLines": "3,4,5,6,7,8,9,83", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "192,287,390,488,588,689,801,8531", "endColumns": "94,102,97,99,100,111,111,100", "endOffsets": "282,385,483,583,684,796,908,8627"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\78e087dd1cdd09d6432d260deb7bf0a8\\transformed\\foundation-release\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,142,233", "endColumns": "86,90,91", "endOffsets": "137,228,320"}, "to": {"startLines": "2,87,88", "startColumns": "4,4,4", "startOffsets": "105,8900,8991", "endColumns": "86,90,91", "endOffsets": "187,8986,9078"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e5d97d34d84112940f02348422c3753d\\transformed\\ui-release\\res\\values-km\\values-km.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "193,278,358,462,560,648,732,815,900,987,1067,1152,1228,1316,1390,1462,1533,1617,1683", "endColumns": "84,79,103,97,87,83,82,84,86,79,84,75,87,73,71,70,83,65,117", "endOffsets": "273,353,457,555,643,727,810,895,982,1062,1147,1223,1311,1385,1457,1528,1612,1678,1796"}, "to": {"startLines": "10,11,12,13,14,15,16,74,75,76,77,78,79,80,81,82,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "913,998,1078,1182,1280,1368,1452,7813,7898,7985,8065,8150,8226,8314,8388,8460,8632,8716,8782", "endColumns": "84,79,103,97,87,83,82,84,86,79,84,75,87,73,71,70,83,65,117", "endOffsets": "993,1073,1177,1275,1363,1447,1530,7893,7980,8060,8145,8221,8309,8383,8455,8526,8711,8777,8895"}}]}]}