{"logs": [{"outputFile": "com.example.everytalk.app-mergeDebugResources-53:/values-mr/values-mr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e5127fbf4aa18facdfc5d332b39b8229\\transformed\\core-1.16.0\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,259,360,463,565,670,787", "endColumns": "99,103,100,102,101,104,116,100", "endOffsets": "150,254,355,458,560,665,782,883"}, "to": {"startLines": "3,4,5,6,7,8,9,83", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "178,278,382,483,586,688,793,8510", "endColumns": "99,103,100,102,101,104,116,100", "endOffsets": "273,377,478,581,683,788,905,8606"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\39e186d1df9a6e64a80555ea95166efd\\transformed\\material3-release\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,180,303,414,539,642,742,857,993,1116,1262,1347,1453,1544,1642,1756,1886,1997,2132,2266,2394,2572,2697,2813,2932,3057,3149,3244,3364,3493,3593,3696,3805,3942,4084,4199,4297,4373,4476,4580,4687,4772,4862,4962,5042,5125,5224,5323,5420,5519,5606,5710,5810,5914,6032,6112,6212", "endColumns": "124,122,110,124,102,99,114,135,122,145,84,105,90,97,113,129,110,134,133,127,177,124,115,118,124,91,94,119,128,99,102,108,136,141,114,97,75,102,103,106,84,89,99,79,82,98,98,96,98,86,103,99,103,117,79,99,93", "endOffsets": "175,298,409,534,637,737,852,988,1111,1257,1342,1448,1539,1637,1751,1881,1992,2127,2261,2389,2567,2692,2808,2927,3052,3144,3239,3359,3488,3588,3691,3800,3937,4079,4194,4292,4368,4471,4575,4682,4767,4857,4957,5037,5120,5219,5318,5415,5514,5601,5705,5805,5909,6027,6107,6207,6301"}, "to": {"startLines": "17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1539,1664,1787,1898,2023,2126,2226,2341,2477,2600,2746,2831,2937,3028,3126,3240,3370,3481,3616,3750,3878,4056,4181,4297,4416,4541,4633,4728,4848,4977,5077,5180,5289,5426,5568,5683,5781,5857,5960,6064,6171,6256,6346,6446,6526,6609,6708,6807,6904,7003,7090,7194,7294,7398,7516,7596,7696", "endColumns": "124,122,110,124,102,99,114,135,122,145,84,105,90,97,113,129,110,134,133,127,177,124,115,118,124,91,94,119,128,99,102,108,136,141,114,97,75,102,103,106,84,89,99,79,82,98,98,96,98,86,103,99,103,117,79,99,93", "endOffsets": "1659,1782,1893,2018,2121,2221,2336,2472,2595,2741,2826,2932,3023,3121,3235,3365,3476,3611,3745,3873,4051,4176,4292,4411,4536,4628,4723,4843,4972,5072,5175,5284,5421,5563,5678,5776,5852,5955,6059,6166,6251,6341,6441,6521,6604,6703,6802,6899,6998,7085,7189,7289,7393,7511,7591,7691,7785"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\78e087dd1cdd09d6432d260deb7bf0a8\\transformed\\foundation-release\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,128,213", "endColumns": "72,84,84", "endOffsets": "123,208,293"}, "to": {"startLines": "2,87,88", "startColumns": "4,4,4", "startOffsets": "105,8871,8956", "endColumns": "72,84,84", "endOffsets": "173,8951,9036"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e5d97d34d84112940f02348422c3753d\\transformed\\ui-release\\res\\values-mr\\values-mr.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "179,273,355,452,550,637,723,808,897,980,1060,1145,1216,1300,1376,1452,1528,1604,1670", "endColumns": "93,81,96,97,86,85,84,88,82,79,84,70,83,75,75,75,75,65,117", "endOffsets": "268,350,447,545,632,718,803,892,975,1055,1140,1211,1295,1371,1447,1523,1599,1665,1783"}, "to": {"startLines": "10,11,12,13,14,15,16,74,75,76,77,78,79,80,81,82,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "910,1004,1086,1183,1281,1368,1454,7790,7879,7962,8042,8127,8198,8282,8358,8434,8611,8687,8753", "endColumns": "93,81,96,97,86,85,84,88,82,79,84,70,83,75,75,75,75,65,117", "endOffsets": "999,1081,1178,1276,1363,1449,1534,7874,7957,8037,8122,8193,8277,8353,8429,8505,8682,8748,8866"}}]}]}