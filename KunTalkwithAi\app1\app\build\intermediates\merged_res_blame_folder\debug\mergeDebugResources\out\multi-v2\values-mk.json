{"logs": [{"outputFile": "com.example.everytalk.app-mergeDebugResources-53:/values-mk/values-mk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e5127fbf4aa18facdfc5d332b39b8229\\transformed\\core-1.16.0\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,450,555,658,774", "endColumns": "97,101,96,97,104,102,115,100", "endOffsets": "148,250,347,445,550,653,769,870"}, "to": {"startLines": "3,4,5,6,7,8,9,83", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "194,292,394,491,589,694,797,8649", "endColumns": "97,101,96,97,104,102,115,100", "endOffsets": "287,389,486,584,689,792,908,8745"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e5d97d34d84112940f02348422c3753d\\transformed\\ui-release\\res\\values-mk\\values-mk.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "195,299,389,485,588,673,750,840,932,1016,1100,1189,1261,1354,1431,1509,1585,1666,1737", "endColumns": "103,89,95,102,84,76,89,91,83,83,88,71,92,76,77,75,80,70,120", "endOffsets": "294,384,480,583,668,745,835,927,1011,1095,1184,1256,1349,1426,1504,1580,1661,1732,1853"}, "to": {"startLines": "10,11,12,13,14,15,16,74,75,76,77,78,79,80,81,82,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "913,1017,1107,1203,1306,1391,1468,7904,7996,8080,8164,8253,8325,8418,8495,8573,8750,8831,8902", "endColumns": "103,89,95,102,84,76,89,91,83,83,88,71,92,76,77,75,80,70,120", "endOffsets": "1012,1102,1198,1301,1386,1463,1553,7991,8075,8159,8248,8320,8413,8490,8568,8644,8826,8897,9018"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\78e087dd1cdd09d6432d260deb7bf0a8\\transformed\\foundation-release\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,144,238", "endColumns": "88,93,95", "endOffsets": "139,233,329"}, "to": {"startLines": "2,87,88", "startColumns": "4,4,4", "startOffsets": "105,9023,9117", "endColumns": "88,93,95", "endOffsets": "189,9112,9208"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\39e186d1df9a6e64a80555ea95166efd\\transformed\\material3-release\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,292,408,526,623,718,830,963,1084,1232,1317,1416,1510,1606,1721,1845,1949,2094,2238,2380,2554,2685,2806,2933,3058,3153,3251,3377,3512,3612,3714,3827,3968,4117,4233,4335,4412,4506,4601,4720,4812,4898,5012,5095,5178,5278,5380,5477,5574,5662,5769,5869,5971,6104,6187,6298", "endColumns": "118,117,115,117,96,94,111,132,120,147,84,98,93,95,114,123,103,144,143,141,173,130,120,126,124,94,97,125,134,99,101,112,140,148,115,101,76,93,94,118,91,85,113,82,82,99,101,96,96,87,106,99,101,132,82,110,102", "endOffsets": "169,287,403,521,618,713,825,958,1079,1227,1312,1411,1505,1601,1716,1840,1944,2089,2233,2375,2549,2680,2801,2928,3053,3148,3246,3372,3507,3607,3709,3822,3963,4112,4228,4330,4407,4501,4596,4715,4807,4893,5007,5090,5173,5273,5375,5472,5569,5657,5764,5864,5966,6099,6182,6293,6396"}, "to": {"startLines": "17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1558,1677,1795,1911,2029,2126,2221,2333,2466,2587,2735,2820,2919,3013,3109,3224,3348,3452,3597,3741,3883,4057,4188,4309,4436,4561,4656,4754,4880,5015,5115,5217,5330,5471,5620,5736,5838,5915,6009,6104,6223,6315,6401,6515,6598,6681,6781,6883,6980,7077,7165,7272,7372,7474,7607,7690,7801", "endColumns": "118,117,115,117,96,94,111,132,120,147,84,98,93,95,114,123,103,144,143,141,173,130,120,126,124,94,97,125,134,99,101,112,140,148,115,101,76,93,94,118,91,85,113,82,82,99,101,96,96,87,106,99,101,132,82,110,102", "endOffsets": "1672,1790,1906,2024,2121,2216,2328,2461,2582,2730,2815,2914,3008,3104,3219,3343,3447,3592,3736,3878,4052,4183,4304,4431,4556,4651,4749,4875,5010,5110,5212,5325,5466,5615,5731,5833,5910,6004,6099,6218,6310,6396,6510,6593,6676,6776,6878,6975,7072,7160,7267,7367,7469,7602,7685,7796,7899"}}]}]}