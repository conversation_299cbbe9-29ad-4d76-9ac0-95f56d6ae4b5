{"logs": [{"outputFile": "com.example.everytalk.app-mergeDebugResources-53:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\33314a664413fc190d8af7aeef943451\\transformed\\appcompat-resources-1.7.0\\res\\values\\values.xml", "from": {"startLines": "2,18,24,34,50", "startColumns": "4,4,4,4,4", "startOffsets": "55,480,658,942,1353", "endLines": "17,23,33,49,53", "endColumns": "24,24,24,24,24", "endOffsets": "475,653,937,1348,1475"}, "to": {"startLines": "282,298,304,492,508", "startColumns": "4,4,4,4,4", "startOffsets": "17060,17485,17663,24493,24904", "endLines": "297,303,313,507,511", "endColumns": "24,24,24,24,24", "endOffsets": "17480,17658,17942,24899,25026"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0f5cff30a180d0faaad1f67045ce7477\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "91,95", "startColumns": "4,4", "startOffsets": "5440,5617", "endColumns": "53,66", "endOffsets": "5489,5679"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\eae94b96c36613b201ce2c6cc3fa87e3\\transformed\\activity-1.10.1\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "96,117", "startColumns": "4,4", "startOffsets": "5684,6785", "endColumns": "41,59", "endOffsets": "5721,6840"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e5127fbf4aa18facdfc5d332b39b8229\\transformed\\core-1.16.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,100,101,105,106,107,108,114,124,159,180,213", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4484,4542,4603,4666,4723,4774,4832,4882,4943,5000,5066,5100,5135,5170,5240,5307,5379,5448,5517,5591,5663,5751,5822,5939,6140,6250,6451,6580,6652,6719,6922,7223,9029,9710,10392", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,99,100,104,105,106,107,113,123,158,179,212,218", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4479,4537,4598,4661,4718,4769,4827,4877,4938,4995,5061,5095,5130,5165,5235,5302,5374,5443,5512,5586,5658,5746,5817,5934,6135,6245,6446,6575,6647,6714,6917,7218,9024,9705,10387,10554"}, "to": {"startLines": "2,11,12,14,15,18,19,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,92,93,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,123,128,129,130,131,132,133,134,218,249,250,254,255,259,263,264,314,320,330,365,386,419", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,546,618,748,813,976,1045,1381,1451,1519,1591,1661,1722,1796,1869,1930,1991,2053,2117,2179,2240,2308,2408,2468,2534,2607,2676,2733,2785,2847,2919,2995,3060,3119,3178,3238,3298,3358,3418,3478,3538,3598,3658,3718,3778,3837,3897,3957,4017,4077,4137,4197,4257,4317,4377,4437,4496,4556,4616,4675,4734,4793,4852,4911,5494,5529,5726,5781,5844,5899,5957,6013,6071,6132,6195,6252,6303,6361,6411,6472,6529,6595,6629,6664,7129,7427,7494,7566,7635,7704,7778,7850,13604,15360,15477,15678,15788,15989,16291,16363,17947,18150,18451,20257,20938,21620", "endLines": "2,11,12,14,15,18,19,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,92,93,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,123,128,129,130,131,132,133,134,218,249,253,254,258,259,263,264,319,329,364,385,418,424", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "205,613,701,808,874,1040,1103,1446,1514,1586,1656,1717,1791,1864,1925,1986,2048,2112,2174,2235,2303,2403,2463,2529,2602,2671,2728,2780,2842,2914,2990,3055,3114,3173,3233,3293,3353,3413,3473,3533,3593,3653,3713,3773,3832,3892,3952,4012,4072,4132,4192,4252,4312,4372,4432,4491,4551,4611,4670,4729,4788,4847,4906,4965,5524,5559,5776,5839,5894,5952,6008,6066,6127,6190,6247,6298,6356,6406,6467,6524,6590,6624,6659,6694,7194,7489,7561,7630,7699,7773,7845,7933,13670,15472,15673,15783,15984,16113,16358,16425,18145,18446,20252,20933,21615,21782"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e5d97d34d84112940f02348422c3753d\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "34,35,36,37,38,39,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,63,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2060,2134,2192,2247,2298,2353,2452,2517,2571,2637,2738,2796,2848,2908,2970,3024,3074,3128,3174,3228,3274,3316,3356,3403,3439,3529,3641,3752", "endLines": "34,35,36,37,38,39,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,62,65,70", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,53,45,41,39,46,35,89,12,12,12", "endOffsets": "2129,2187,2242,2293,2348,2401,2512,2566,2632,2733,2791,2843,2903,2965,3019,3069,3123,3169,3223,3269,3311,3351,3398,3434,3524,3636,3747,4004"}, "to": {"startLines": "83,86,87,89,90,121,136,137,141,142,143,144,145,207,210,211,212,213,214,215,216,217,219,220,221,225,241,244", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4970,5155,5213,5334,5385,7003,7994,8059,8251,8317,8418,8476,8528,13029,13218,13272,13322,13376,13422,13476,13522,13564,13675,13722,13758,14012,14992,15103", "endLines": "83,86,87,89,90,121,136,137,141,142,143,144,145,207,210,211,212,213,214,215,216,217,219,220,221,227,243,248", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,53,45,41,39,46,35,89,12,12,12", "endOffsets": "5039,5208,5263,5380,5435,7051,8054,8108,8312,8413,8471,8523,8583,13086,13267,13317,13371,13417,13471,13517,13559,13599,13717,13753,13843,14119,15098,15355"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\55a18c243f4ac882d93d3a7cd9a80296\\transformed\\navigation-runtime-2.7.7\\res\\values\\values.xml", "from": {"startLines": "2,3,10,13", "startColumns": "4,4,4,4", "startOffsets": "55,108,412,527", "endLines": "2,9,12,15", "endColumns": "52,24,24,24", "endOffsets": "103,407,522,637"}, "to": {"startLines": "94,271,453,456", "startColumns": "4,4,4,4", "startOffsets": "5564,16625,23050,23165", "endLines": "94,277,455,458", "endColumns": "52,24,24,24", "endOffsets": "5612,16924,23160,23275"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\b6391c467cc8d37a1fceadaf9559aef0\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "119", "startColumns": "4", "startOffsets": "6899", "endColumns": "49", "endOffsets": "6944"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ebf5f12d179771782bf41f123e88642d\\transformed\\navigation-common-2.7.7\\res\\values\\values.xml", "from": {"startLines": "2,15,21,27,30", "startColumns": "4,4,4,4,4", "startOffsets": "55,694,938,1185,1318", "endLines": "14,20,26,29,34", "endColumns": "24,24,24,24,24", "endOffsets": "689,933,1180,1313,1495"}, "to": {"startLines": "425,438,444,450,459", "startColumns": "4,4,4,4,4", "startOffsets": "21787,22426,22670,22917,23280", "endLines": "437,443,449,452,463", "endColumns": "24,24,24,24,24", "endOffsets": "22421,22665,22912,23045,23457"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\39ccfaa4b46a364def42306f3c4848c5\\transformed\\ui-graphics-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "65", "endOffsets": "116"}, "to": {"startLines": "88", "startColumns": "4", "startOffsets": "5268", "endColumns": "65", "endOffsets": "5329"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c2fe2863a9c50634a9253e6514925e3b\\transformed\\window-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,7,8,9,10,11,19,23,34,51", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,234,294,346,391,451,869,1066,1790,2904", "endLines": "6,7,8,9,10,18,22,33,50,58", "endColumns": "11,59,51,44,59,24,24,24,24,24", "endOffsets": "229,289,341,386,446,864,1061,1785,2899,3292"}, "to": {"startLines": "3,8,9,10,84,265,278,464,472,484", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "210,389,449,501,5044,16430,16929,23462,23744,24184", "endLines": "7,8,9,10,84,270,281,471,483,491", "endColumns": "11,59,51,44,59,24,24,24,24,24", "endOffsets": "384,444,496,541,5099,16620,17055,23739,24179,24488"}}, {"source": "C:\\Users\\<USER>\\Desktop\\anyaitotalked\\KunTalkwithAi\\app1\\app\\src\\main\\res\\values\\ktalk.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "57", "endColumns": "39", "endOffsets": "92"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "936", "endColumns": "39", "endOffsets": "971"}}, {"source": "C:\\Users\\<USER>\\Desktop\\anyaitotalked\\KunTalkwithAi\\app1\\app\\src\\main\\res\\values\\ids.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "57", "endColumns": "54", "endOffsets": "107"}, "to": {"startLines": "120", "startColumns": "4", "startOffsets": "6949", "endColumns": "53", "endOffsets": "6998"}}, {"source": "C:\\Users\\<USER>\\Desktop\\anyaitotalked\\KunTalkwithAi\\app1\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "10,1,9,4,3,5,8,7,6", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "481,17,424,157,100,204,358,295,241", "endColumns": "51,46,55,45,55,35,64,61,52", "endOffsets": "528,59,475,198,151,235,418,352,289"}, "to": {"startLines": "124,126,135,138,139,140,208,209,224", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7199,7334,7938,8113,8159,8215,13091,13156,13959", "endColumns": "51,46,55,45,55,35,64,61,52", "endOffsets": "7246,7376,7989,8154,8210,8246,13151,13213,14007"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c1ff346b9db1ba5563c0e48712d638e7\\transformed\\core-viewtree-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "115", "startColumns": "4", "startOffsets": "6699", "endColumns": "42", "endOffsets": "6737"}}, {"source": "C:\\Users\\<USER>\\Desktop\\anyaitotalked\\KunTalkwithAi\\app1\\app\\src\\main\\res\\values\\ic_launcher_background.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "57", "endColumns": "56", "endOffsets": "109"}, "to": {"startLines": "16", "startColumns": "4", "startOffsets": "879", "endColumns": "56", "endOffsets": "931"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0c42c46107fd561085aaed561c4cf813\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "116", "startColumns": "4", "startOffsets": "6742", "endColumns": "42", "endOffsets": "6780"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8855094b50844372810db01026290202\\transformed\\coil-core-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "50", "endOffsets": "101"}, "to": {"startLines": "85", "startColumns": "4", "startOffsets": "5104", "endColumns": "50", "endOffsets": "5150"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f9050601dae5b1dbcce96a44cee8f8ed\\transformed\\savedstate-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "118", "startColumns": "4", "startOffsets": "6845", "endColumns": "53", "endOffsets": "6894"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\78e087dd1cdd09d6432d260deb7bf0a8\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,101,157", "endColumns": "45,55,54", "endOffsets": "96,152,207"}, "to": {"startLines": "127,222,223", "startColumns": "4,4,4", "startOffsets": "7381,13848,13904", "endColumns": "45,55,54", "endOffsets": "7422,13899,13954"}}, {"source": "C:\\Users\\<USER>\\Desktop\\anyaitotalked\\KunTalkwithAi\\app1\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "59", "endLines": "5", "endColumns": "12", "endOffsets": "229"}, "to": {"startLines": "260", "startColumns": "4", "startOffsets": "16118", "endLines": "262", "endColumns": "12", "endOffsets": "16286"}}, {"source": "C:\\Users\\<USER>\\Desktop\\anyaitotalked\\KunTalkwithAi\\app1\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "7,2,3,4,5,6,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "293,57,105,153,201,247,336", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "330,99,147,195,241,287,373"}, "to": {"startLines": "13,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "706,1108,1155,1202,1249,1294,1339", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "743,1150,1197,1244,1289,1334,1376"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\39e186d1df9a6e64a80555ea95166efd\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,15,16,17,18,19,20,21,22,23,24,25,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,173,261,347,428,512,581,646,729,835,921,1041,1095,1164,1225,1294,1383,1478,1552,1649,1742,1840,1989,2080,2168,2264,2362,2426,2494,2581,2675,2742,2814,2886,2987,3096,3172,3241,3289,3355,3419,3493,3550,3607,3679,3729,3783,3854,3925,3995,4064,4122,4198,4269,4343,4429,4479,4549,4614,5329", "endLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,73,76", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "168,256,342,423,507,576,641,724,830,916,1036,1090,1159,1220,1289,1378,1473,1547,1644,1737,1835,1984,2075,2163,2259,2357,2421,2489,2576,2670,2737,2809,2881,2982,3091,3167,3236,3284,3350,3414,3488,3545,3602,3674,3724,3778,3849,3920,3990,4059,4117,4193,4264,4338,4424,4474,4544,4609,5324,5477"}, "to": {"startLines": "122,146,147,148,149,150,151,152,153,154,155,158,159,160,161,162,163,164,165,166,167,168,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,228,238", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7056,8588,8676,8762,8843,8927,8996,9061,9144,9250,9336,9456,9510,9579,9640,9709,9798,9893,9967,10064,10157,10255,10404,10495,10583,10679,10777,10841,10909,10996,11090,11157,11229,11301,11402,11511,11587,11656,11704,11770,11834,11908,11965,12022,12094,12144,12198,12269,12340,12410,12479,12537,12613,12684,12758,12844,12894,12964,14124,14839", "endLines": "122,146,147,148,149,150,151,152,153,154,157,158,159,160,161,162,163,164,165,166,167,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,237,240", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "7124,8671,8757,8838,8922,8991,9056,9139,9245,9331,9451,9505,9574,9635,9704,9793,9888,9962,10059,10152,10250,10399,10490,10578,10674,10772,10836,10904,10991,11085,11152,11224,11296,11397,11506,11582,11651,11699,11765,11829,11903,11960,12017,12089,12139,12193,12264,12335,12405,12474,12532,12608,12679,12753,12839,12889,12959,13024,14834,14987"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\6d19ceabcd00b3e0d82ee2aacc706da1\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "125", "startColumns": "4", "startOffsets": "7251", "endColumns": "82", "endOffsets": "7329"}}]}]}