{"logs": [{"outputFile": "com.example.everytalk.app-mergeDebugResources-53:/values-ta/values-ta.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e5d97d34d84112940f02348422c3753d\\transformed\\ui-release\\res\\values-ta\\values-ta.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "183,280,364,458,559,650,733,842,933,1028,1110,1196,1286,1378,1463,1536,1607,1687,1756", "endColumns": "96,83,93,100,90,82,108,90,94,81,85,89,91,84,72,70,79,68,119", "endOffsets": "275,359,453,554,645,728,837,928,1023,1105,1191,1281,1373,1458,1531,1602,1682,1751,1871"}, "to": {"startLines": "10,11,12,13,14,15,16,74,75,76,77,78,79,80,81,82,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "928,1025,1109,1203,1304,1395,1478,8166,8257,8352,8434,8520,8610,8702,8787,8860,9032,9112,9181", "endColumns": "96,83,93,100,90,82,108,90,94,81,85,89,91,84,72,70,79,68,119", "endOffsets": "1020,1104,1198,1299,1390,1473,1582,8252,8347,8429,8515,8605,8697,8782,8855,8926,9107,9176,9296"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\78e087dd1cdd09d6432d260deb7bf0a8\\transformed\\foundation-release\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,132,225", "endColumns": "76,92,97", "endOffsets": "127,220,318"}, "to": {"startLines": "2,87,88", "startColumns": "4,4,4", "startOffsets": "105,9301,9394", "endColumns": "76,92,97", "endOffsets": "177,9389,9487"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\39e186d1df9a6e64a80555ea95166efd\\transformed\\material3-release\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,181,307,428,552,653,749,862,1013,1144,1285,1369,1473,1573,1681,1798,1921,2030,2176,2320,2454,2660,2789,2910,3035,3181,3282,3380,3526,3662,3768,3881,3988,4134,4286,4395,4507,4585,4687,4790,4907,4993,5086,5199,5279,5367,5466,5586,5681,5786,5875,5997,6101,6208,6341,6421,6532", "endColumns": "125,125,120,123,100,95,112,150,130,140,83,103,99,107,116,122,108,145,143,133,205,128,120,124,145,100,97,145,135,105,112,106,145,151,108,111,77,101,102,116,85,92,112,79,87,98,119,94,104,88,121,103,106,132,79,110,101", "endOffsets": "176,302,423,547,648,744,857,1008,1139,1280,1364,1468,1568,1676,1793,1916,2025,2171,2315,2449,2655,2784,2905,3030,3176,3277,3375,3521,3657,3763,3876,3983,4129,4281,4390,4502,4580,4682,4785,4902,4988,5081,5194,5274,5362,5461,5581,5676,5781,5870,5992,6096,6203,6336,6416,6527,6629"}, "to": {"startLines": "17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1587,1713,1839,1960,2084,2185,2281,2394,2545,2676,2817,2901,3005,3105,3213,3330,3453,3562,3708,3852,3986,4192,4321,4442,4567,4713,4814,4912,5058,5194,5300,5413,5520,5666,5818,5927,6039,6117,6219,6322,6439,6525,6618,6731,6811,6899,6998,7118,7213,7318,7407,7529,7633,7740,7873,7953,8064", "endColumns": "125,125,120,123,100,95,112,150,130,140,83,103,99,107,116,122,108,145,143,133,205,128,120,124,145,100,97,145,135,105,112,106,145,151,108,111,77,101,102,116,85,92,112,79,87,98,119,94,104,88,121,103,106,132,79,110,101", "endOffsets": "1708,1834,1955,2079,2180,2276,2389,2540,2671,2812,2896,3000,3100,3208,3325,3448,3557,3703,3847,3981,4187,4316,4437,4562,4708,4809,4907,5053,5189,5295,5408,5515,5661,5813,5922,6034,6112,6214,6317,6434,6520,6613,6726,6806,6894,6993,7113,7208,7313,7402,7524,7628,7735,7868,7948,8059,8161"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e5127fbf4aa18facdfc5d332b39b8229\\transformed\\core-1.16.0\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,353,451,558,673,801", "endColumns": "95,102,98,97,106,114,127,100", "endOffsets": "146,249,348,446,553,668,796,897"}, "to": {"startLines": "3,4,5,6,7,8,9,83", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "182,278,381,480,578,685,800,8931", "endColumns": "95,102,98,97,106,114,127,100", "endOffsets": "273,376,475,573,680,795,923,9027"}}]}]}