{"logs": [{"outputFile": "com.example.everytalk.app-mergeDebugResources-53:/values-zu/values-zu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e5127fbf4aa18facdfc5d332b39b8229\\transformed\\core-1.16.0\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,257,356,459,565,672,785", "endColumns": "97,103,98,102,105,106,112,100", "endOffsets": "148,252,351,454,560,667,780,881"}, "to": {"startLines": "3,4,5,6,7,8,9,83", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "200,298,402,501,604,710,817,8655", "endColumns": "97,103,98,102,105,106,112,100", "endOffsets": "293,397,496,599,705,812,925,8751"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\39e186d1df9a6e64a80555ea95166efd\\transformed\\material3-release\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,292,407,524,627,725,840,977,1094,1249,1334,1434,1526,1627,1747,1869,1974,2118,2253,2390,2562,2694,2820,2945,3073,3166,3266,3394,3536,3635,3737,3846,3986,4127,4237,4339,4417,4512,4609,4717,4803,4889,4995,5075,5160,5268,5370,5474,5572,5660,5766,5872,5974,6096,6176,6283", "endColumns": "117,118,114,116,102,97,114,136,116,154,84,99,91,100,119,121,104,143,134,136,171,131,125,124,127,92,99,127,141,98,101,108,139,140,109,101,77,94,96,107,85,85,105,79,84,107,101,103,97,87,105,105,101,121,79,106,99", "endOffsets": "168,287,402,519,622,720,835,972,1089,1244,1329,1429,1521,1622,1742,1864,1969,2113,2248,2385,2557,2689,2815,2940,3068,3161,3261,3389,3531,3630,3732,3841,3981,4122,4232,4334,4412,4507,4604,4712,4798,4884,4990,5070,5155,5263,5365,5469,5567,5655,5761,5867,5969,6091,6171,6278,6378"}, "to": {"startLines": "17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1578,1696,1815,1930,2047,2150,2248,2363,2500,2617,2772,2857,2957,3049,3150,3270,3392,3497,3641,3776,3913,4085,4217,4343,4468,4596,4689,4789,4917,5059,5158,5260,5369,5509,5650,5760,5862,5940,6035,6132,6240,6326,6412,6518,6598,6683,6791,6893,6997,7095,7183,7289,7395,7497,7619,7699,7806", "endColumns": "117,118,114,116,102,97,114,136,116,154,84,99,91,100,119,121,104,143,134,136,171,131,125,124,127,92,99,127,141,98,101,108,139,140,109,101,77,94,96,107,85,85,105,79,84,107,101,103,97,87,105,105,101,121,79,106,99", "endOffsets": "1691,1810,1925,2042,2145,2243,2358,2495,2612,2767,2852,2952,3044,3145,3265,3387,3492,3636,3771,3908,4080,4212,4338,4463,4591,4684,4784,4912,5054,5153,5255,5364,5504,5645,5755,5857,5935,6030,6127,6235,6321,6407,6513,6593,6678,6786,6888,6992,7090,7178,7284,7390,7492,7614,7694,7801,7901"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\78e087dd1cdd09d6432d260deb7bf0a8\\transformed\\foundation-release\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,150,241", "endColumns": "94,90,91", "endOffsets": "145,236,328"}, "to": {"startLines": "2,87,88", "startColumns": "4,4,4", "startOffsets": "105,9029,9120", "endColumns": "94,90,91", "endOffsets": "195,9115,9207"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e5d97d34d84112940f02348422c3753d\\transformed\\ui-release\\res\\values-zu\\values-zu.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "201,296,378,483,588,678,760,849,942,1025,1113,1201,1277,1366,1447,1523,1598,1677,1747", "endColumns": "94,81,104,104,89,81,88,92,82,87,87,75,88,80,75,74,78,69,123", "endOffsets": "291,373,478,583,673,755,844,937,1020,1108,1196,1272,1361,1442,1518,1593,1672,1742,1866"}, "to": {"startLines": "10,11,12,13,14,15,16,74,75,76,77,78,79,80,81,82,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "930,1025,1107,1212,1317,1407,1489,7906,7999,8082,8170,8258,8334,8423,8504,8580,8756,8835,8905", "endColumns": "94,81,104,104,89,81,88,92,82,87,87,75,88,80,75,74,78,69,123", "endOffsets": "1020,1102,1207,1312,1402,1484,1573,7994,8077,8165,8253,8329,8418,8499,8575,8650,8830,8900,9024"}}]}]}