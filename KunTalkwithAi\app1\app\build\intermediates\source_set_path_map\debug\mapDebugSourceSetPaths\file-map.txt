com.example.everytalk.app-material-release-0 C:\Users\<USER>\.gradle\caches\8.13\transforms\01e4e2faf1698c602461ff28c59f0275\transformed\material-release\res
com.example.everytalk.app-lifecycle-viewmodel-savedstate-release-1 C:\Users\<USER>\.gradle\caches\8.13\transforms\08f0224dd2f3ac7f7ec5781649a0439f\transformed\lifecycle-viewmodel-savedstate-release\res
com.example.everytalk.app-lifecycle-runtime-release-2 C:\Users\<USER>\.gradle\caches\8.13\transforms\0c42c46107fd561085aaed561c4cf813\transformed\lifecycle-runtime-release\res
com.example.everytalk.app-customview-poolingcontainer-1.0.0-3 C:\Users\<USER>\.gradle\caches\8.13\transforms\0f5cff30a180d0faaad1f67045ce7477\transformed\customview-poolingcontainer-1.0.0\res
com.example.everytalk.app-ui-unit-release-4 C:\Users\<USER>\.gradle\caches\8.13\transforms\10707ed4b16513a5d686adbcdd93d18f\transformed\ui-unit-release\res
com.example.everytalk.app-tracing-1.2.0-5 C:\Users\<USER>\.gradle\caches\8.13\transforms\13a3bbc9656c8aa176373f0021fcd2e2\transformed\tracing-1.2.0\res
com.example.everytalk.app-lifecycle-runtime-compose-release-6 C:\Users\<USER>\.gradle\caches\8.13\transforms\178ff5c54d6bc8a1dbeb66f819bb9e8e\transformed\lifecycle-runtime-compose-release\res
com.example.everytalk.app-savedstate-ktx-1.3.0-7 C:\Users\<USER>\.gradle\caches\8.13\transforms\27eb01bccc439fad4c431fe11744a39c\transformed\savedstate-ktx-1.3.0\res
com.example.everytalk.app-activity-compose-1.10.1-8 C:\Users\<USER>\.gradle\caches\8.13\transforms\2d93cf781d3eb9d67a9f1152cdf6b002\transformed\activity-compose-1.10.1\res
com.example.everytalk.app-core-ktx-1.16.0-9 C:\Users\<USER>\.gradle\caches\8.13\transforms\32301300fe1d8bb38b9b0d036470e825\transformed\core-ktx-1.16.0\res
com.example.everytalk.app-appcompat-resources-1.7.0-10 C:\Users\<USER>\.gradle\caches\8.13\transforms\33314a664413fc190d8af7aeef943451\transformed\appcompat-resources-1.7.0\res
com.example.everytalk.app-foundation-layout-release-11 C:\Users\<USER>\.gradle\caches\8.13\transforms\366abc712e29383e40ce99631f098d2f\transformed\foundation-layout-release\res
com.example.everytalk.app-ui-graphics-release-12 C:\Users\<USER>\.gradle\caches\8.13\transforms\39ccfaa4b46a364def42306f3c4848c5\transformed\ui-graphics-release\res
com.example.everytalk.app-material3-release-13 C:\Users\<USER>\.gradle\caches\8.13\transforms\39e186d1df9a6e64a80555ea95166efd\transformed\material3-release\res
com.example.everytalk.app-lifecycle-viewmodel-compose-release-14 C:\Users\<USER>\.gradle\caches\8.13\transforms\416368b85810fee672d1adc3adb76197\transformed\lifecycle-viewmodel-compose-release\res
com.example.everytalk.app-annotation-experimental-1.4.1-15 C:\Users\<USER>\.gradle\caches\8.13\transforms\44dfff852f8446f00028c43f19613595\transformed\annotation-experimental-1.4.1\res
com.example.everytalk.app-runtime-release-16 C:\Users\<USER>\.gradle\caches\8.13\transforms\44e7bd1b86c32aef1e8297894b9d3141\transformed\runtime-release\res
com.example.everytalk.app-navigation-runtime-ktx-2.7.7-17 C:\Users\<USER>\.gradle\caches\8.13\transforms\4fc72a0b32a789589bb4bfb0ac14697c\transformed\navigation-runtime-ktx-2.7.7\res
com.example.everytalk.app-ui-test-manifest-1.8.0-18 C:\Users\<USER>\.gradle\caches\8.13\transforms\54a6656068f8f5c44960de6cc4931323\transformed\ui-test-manifest-1.8.0\res
com.example.everytalk.app-navigation-runtime-2.7.7-19 C:\Users\<USER>\.gradle\caches\8.13\transforms\55a18c243f4ac882d93d3a7cd9a80296\transformed\navigation-runtime-2.7.7\res
com.example.everytalk.app-lifecycle-livedata-core-2.9.0-20 C:\Users\<USER>\.gradle\caches\8.13\transforms\56673a8b4479bfbc56575321e60b5dc5\transformed\lifecycle-livedata-core-2.9.0\res
com.example.everytalk.app-material-icons-core-release-21 C:\Users\<USER>\.gradle\caches\8.13\transforms\57c8256c9aa49c73e70860005aae44e1\transformed\material-icons-core-release\res
com.example.everytalk.app-lifecycle-process-2.9.0-22 C:\Users\<USER>\.gradle\caches\8.13\transforms\58eeb28f86695ac6888d7afb81f7d4a5\transformed\lifecycle-process-2.9.0\res
com.example.everytalk.app-animation-core-release-23 C:\Users\<USER>\.gradle\caches\8.13\transforms\5d8d6e1aab7e26ebc2fe0647a99d7826\transformed\animation-core-release\res
com.example.everytalk.app-navigation-common-ktx-2.7.7-24 C:\Users\<USER>\.gradle\caches\8.13\transforms\69f7b49adaa0dc41678f40a22d832f89\transformed\navigation-common-ktx-2.7.7\res
com.example.everytalk.app-profileinstaller-1.4.1-25 C:\Users\<USER>\.gradle\caches\8.13\transforms\6aab0203995ed8c83282f13742e3c95f\transformed\profileinstaller-1.4.1\res
com.example.everytalk.app-startup-runtime-1.1.1-26 C:\Users\<USER>\.gradle\caches\8.13\transforms\6d19ceabcd00b3e0d82ee2aacc706da1\transformed\startup-runtime-1.1.1\res
com.example.everytalk.app-graphics-path-1.0.1-27 C:\Users\<USER>\.gradle\caches\8.13\transforms\7012401d2baa5baca522f517fb27a57e\transformed\graphics-path-1.0.1\res
com.example.everytalk.app-ui-tooling-release-28 C:\Users\<USER>\.gradle\caches\8.13\transforms\77f3324e26b0fc7ae8260a8c77bc3ce8\transformed\ui-tooling-release\res
com.example.everytalk.app-foundation-release-29 C:\Users\<USER>\.gradle\caches\8.13\transforms\78e087dd1cdd09d6432d260deb7bf0a8\transformed\foundation-release\res
com.example.everytalk.app-coil-core-release-30 C:\Users\<USER>\.gradle\caches\8.13\transforms\8855094b50844372810db01026290202\transformed\coil-core-release\res
com.example.everytalk.app-emoji2-1.4.0-31 C:\Users\<USER>\.gradle\caches\8.13\transforms\8d9770e3d771c462ed39db07884dbd01\transformed\emoji2-1.4.0\res
com.example.everytalk.app-activity-ktx-1.10.1-32 C:\Users\<USER>\.gradle\caches\8.13\transforms\9cb287a452abedb26e4c003e4e8fe9db\transformed\activity-ktx-1.10.1\res
com.example.everytalk.app-core-runtime-2.2.0-33 C:\Users\<USER>\.gradle\caches\8.13\transforms\aae976a0f67053f6b2d2ba71dceadfc4\transformed\core-runtime-2.2.0\res
com.example.everytalk.app-runtime-saveable-release-34 C:\Users\<USER>\.gradle\caches\8.13\transforms\ac027c6e0e0240bf4974a08c7329219d\transformed\runtime-saveable-release\res
com.example.everytalk.app-exifinterface-1.4.1-35 C:\Users\<USER>\.gradle\caches\8.13\transforms\b027bcfc2bbe3124197624f8b76ed37c\transformed\exifinterface-1.4.1\res
com.example.everytalk.app-lifecycle-viewmodel-release-36 C:\Users\<USER>\.gradle\caches\8.13\transforms\b6391c467cc8d37a1fceadaf9559aef0\transformed\lifecycle-viewmodel-release\res
com.example.everytalk.app-material-icons-extended-release-37 C:\Users\<USER>\.gradle\caches\8.13\transforms\b64d6959e2736c03e82d34c3fee8f775\transformed\material-icons-extended-release\res
com.example.everytalk.app-core-viewtree-1.0.0-38 C:\Users\<USER>\.gradle\caches\8.13\transforms\c1ff346b9db1ba5563c0e48712d638e7\transformed\core-viewtree-1.0.0\res
com.example.everytalk.app-window-1.0.0-39 C:\Users\<USER>\.gradle\caches\8.13\transforms\c2fe2863a9c50634a9253e6514925e3b\transformed\window-1.0.0\res
com.example.everytalk.app-navigation-compose-2.7.7-40 C:\Users\<USER>\.gradle\caches\8.13\transforms\d916986582d5b8a927bbf5cebcb93f32\transformed\navigation-compose-2.7.7\res
com.example.everytalk.app-lifecycle-viewmodel-ktx-2.9.0-41 C:\Users\<USER>\.gradle\caches\8.13\transforms\db62b0c2343926056a7d6419eca539f7\transformed\lifecycle-viewmodel-ktx-2.9.0\res
com.example.everytalk.app-ui-util-release-42 C:\Users\<USER>\.gradle\caches\8.13\transforms\df891fc547f4739d128cc2de1961a0ac\transformed\ui-util-release\res
com.example.everytalk.app-core-1.16.0-43 C:\Users\<USER>\.gradle\caches\8.13\transforms\e5127fbf4aa18facdfc5d332b39b8229\transformed\core-1.16.0\res
com.example.everytalk.app-ui-release-44 C:\Users\<USER>\.gradle\caches\8.13\transforms\e5d97d34d84112940f02348422c3753d\transformed\ui-release\res
com.example.everytalk.app-activity-1.10.1-45 C:\Users\<USER>\.gradle\caches\8.13\transforms\eae94b96c36613b201ce2c6cc3fa87e3\transformed\activity-1.10.1\res
com.example.everytalk.app-navigation-common-2.7.7-46 C:\Users\<USER>\.gradle\caches\8.13\transforms\ebf5f12d179771782bf41f123e88642d\transformed\navigation-common-2.7.7\res
com.example.everytalk.app-material3-window-size-class-release-47 C:\Users\<USER>\.gradle\caches\8.13\transforms\f563ddd6287c157039d74db0ad1cb0e5\transformed\material3-window-size-class-release\res
com.example.everytalk.app-savedstate-release-48 C:\Users\<USER>\.gradle\caches\8.13\transforms\f9050601dae5b1dbcce96a44cee8f8ed\transformed\savedstate-release\res
com.example.everytalk.app-ui-text-release-49 C:\Users\<USER>\.gradle\caches\8.13\transforms\fb8e9ee106f64909a48113e968a603c5\transformed\ui-text-release\res
com.example.everytalk.app-material-ripple-release-50 C:\Users\<USER>\.gradle\caches\8.13\transforms\fcd20e835c8bf1213f25bc00fd020fc0\transformed\material-ripple-release\res
com.example.everytalk.app-resValues-51 C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\build\generated\res\resValues\debug
com.example.everytalk.app-packageDebugResources-52 C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
com.example.everytalk.app-packageDebugResources-53 C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.example.everytalk.app-debug-54 C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\build\intermediates\merged_res\debug\mergeDebugResources
com.example.everytalk.app-debug-55 C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\debug\res
com.example.everytalk.app-main-56 C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\res
