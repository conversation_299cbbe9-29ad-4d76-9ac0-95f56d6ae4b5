__pycache__/
*.py[cod]
*$py.class

*.so

.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib60/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

*.manifest
*.spec

pip-log.txt
pip-delete-this-directory.txt

htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

*.mo
*.pot

*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

instance/
.webassets-cache

.scrapy

docs/_build/

target/

.ipynb_checkpoints

profile_default/
ipython_config.py

__pypackages__/

celerybeat-schedule
celerybeat.pid

*.sage.py

.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

.spyderproject
.spyproject

.ropeproject

/site

.mypy_cache/
.dmypy.json
dmypy.json

.pyre/

.pytype/

cython_debug/

.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
*.code-workspace

*.sublime-project
*.sublime-workspace

.idea/

.project
.pydevproject

.DS_Store

Thumbs.db

logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

temp_document_uploads/
.vercel/

gen-lang-client-0550427988-f31c34047676.json

docker-compose.override.yml
docker-compose.yaml.local
docker-compose.yml.local
