{"logs": [{"outputFile": "com.example.everytalk.app-mergeDebugResources-53:/values-de/values-de.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e5127fbf4aa18facdfc5d332b39b8229\\transformed\\core-1.16.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,455,563,668,786", "endColumns": "97,101,99,99,107,104,117,100", "endOffsets": "148,250,350,450,558,663,781,882"}, "to": {"startLines": "3,4,5,6,7,8,9,83", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "193,291,393,493,593,701,806,8671", "endColumns": "97,101,99,99,107,104,117,100", "endOffsets": "286,388,488,588,696,801,919,8767"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\39e186d1df9a6e64a80555ea95166efd\\transformed\\material3-release\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,186,315,424,553,663,758,870,1014,1132,1288,1373,1478,1573,1675,1793,1919,2029,2165,2302,2437,2616,2744,2867,2995,3120,3216,3314,3434,3563,3663,3768,3870,4011,4159,4265,4367,4447,4543,4638,4758,4844,4933,5034,5114,5200,5300,5406,5501,5602,5690,5799,5900,6004,6142,6231,6336", "endColumns": "130,128,108,128,109,94,111,143,117,155,84,104,94,101,117,125,109,135,136,134,178,127,122,127,124,95,97,119,128,99,104,101,140,147,105,101,79,95,94,119,85,88,100,79,85,99,105,94,100,87,108,100,103,137,88,104,95", "endOffsets": "181,310,419,548,658,753,865,1009,1127,1283,1368,1473,1568,1670,1788,1914,2024,2160,2297,2432,2611,2739,2862,2990,3115,3211,3309,3429,3558,3658,3763,3865,4006,4154,4260,4362,4442,4538,4633,4753,4839,4928,5029,5109,5195,5295,5401,5496,5597,5685,5794,5895,5999,6137,6226,6331,6427"}, "to": {"startLines": "17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1570,1701,1830,1939,2068,2178,2273,2385,2529,2647,2803,2888,2993,3088,3190,3308,3434,3544,3680,3817,3952,4131,4259,4382,4510,4635,4731,4829,4949,5078,5178,5283,5385,5526,5674,5780,5882,5962,6058,6153,6273,6359,6448,6549,6629,6715,6815,6921,7016,7117,7205,7314,7415,7519,7657,7746,7851", "endColumns": "130,128,108,128,109,94,111,143,117,155,84,104,94,101,117,125,109,135,136,134,178,127,122,127,124,95,97,119,128,99,104,101,140,147,105,101,79,95,94,119,85,88,100,79,85,99,105,94,100,87,108,100,103,137,88,104,95", "endOffsets": "1696,1825,1934,2063,2173,2268,2380,2524,2642,2798,2883,2988,3083,3185,3303,3429,3539,3675,3812,3947,4126,4254,4377,4505,4630,4726,4824,4944,5073,5173,5278,5380,5521,5669,5775,5877,5957,6053,6148,6268,6354,6443,6544,6624,6710,6810,6916,7011,7112,7200,7309,7410,7514,7652,7741,7846,7942"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\78e087dd1cdd09d6432d260deb7bf0a8\\transformed\\foundation-release\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,143,230", "endColumns": "87,86,89", "endOffsets": "138,225,315"}, "to": {"startLines": "2,87,88", "startColumns": "4,4,4", "startOffsets": "105,9037,9124", "endColumns": "87,86,89", "endOffsets": "188,9119,9209"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e5d97d34d84112940f02348422c3753d\\transformed\\ui-release\\res\\values-de\\values-de.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "194,290,378,476,576,663,748,840,929,1017,1098,1182,1257,1347,1422,1494,1564,1643,1709", "endColumns": "95,87,97,99,86,84,91,88,87,80,83,74,89,74,71,69,78,65,119", "endOffsets": "285,373,471,571,658,743,835,924,1012,1093,1177,1252,1342,1417,1489,1559,1638,1704,1824"}, "to": {"startLines": "10,11,12,13,14,15,16,74,75,76,77,78,79,80,81,82,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "924,1020,1108,1206,1306,1393,1478,7947,8036,8124,8205,8289,8364,8454,8529,8601,8772,8851,8917", "endColumns": "95,87,97,99,86,84,91,88,87,80,83,74,89,74,71,69,78,65,119", "endOffsets": "1015,1103,1201,1301,1388,1473,1565,8031,8119,8200,8284,8359,8449,8524,8596,8666,8846,8912,9032"}}]}]}