# AI输出格式规范 (AI Output Format Specification)

## 概述
本文档定义了AI输出的标准格式规范，确保AI输出内容的一致性、可读性和可处理性。

## 版本信息
- 规范版本: 1.0
- 更新日期: 2025-01-25
- 适用范围: 所有AI模型输出

## 核心原则

1. **一致性**: 相同类型的内容使用相同的格式
2. **可读性**: 输出对人类用户友好，格式清晰
3. **可解析性**: 机器可以准确解析和处理
4. **容错性**: 即使格式有误也能自动修复

## 格式规范

### 1. 数学公式格式

#### 行内数学公式
- **标准格式**: `$formula$`
- **示例**: 
  - 正确: `勾股定理: $a^2 + b^2 = c^2$`
  - 错误: `勾股定理: a^2 + b^2 = c^2`

#### 块级数学公式
- **标准格式**: `$$formula$$`
- **示例**:
```
$$
e^{i\pi} + 1 = 0
$$
```

#### 常见数学表达式规范
- **指数**: `$x^{n}$` 而不是 `x^n`
- **分数**: `$\frac{a}{b}$` 而不是 `a/b`
- **开方**: `$\sqrt{x}$` 而不是 `√x`
- **下标**: `$a_{i}$` 而不是 `a_i`

### 2. 代码格式

#### 行内代码
- **标准格式**: `` `code` ``
- **示例**: `使用 `print()` 函数输出内容`

#### 代码块
- **标准格式**: 
```
```language
code content
```
```

- **必须包含语言标识**:
  - Python: `python`
  - JavaScript: `javascript`
  - Java: `java`
  - 其他: `text`

### 3. Markdown格式

#### 标题
- **格式**: `# 标题` (# 后必须有空格)
- **层级**: 使用1-6个#表示不同层级
- **示例**:
```
# 一级标题
## 二级标题
### 三级标题
```

#### 列表
- **无序列表**: `- 项目` (- 后必须有空格)
- **有序列表**: `1. 项目` (数字后必须有空格)
- **示例**:
```
- 第一项
- 第二项
  - 子项目

1. 第一步
2. 第二步
```

#### 链接
- **格式**: `[显示文本](URL)`
- **示例**: `[GitHub](https://github.com)`

#### 引用
- **格式**: `> 引用内容` (> 后必须有空格)
- **示例**:
```
> 这是一个引用
> 可以跨多行
```

### 4. JSON格式

#### 基本要求
- 必须符合标准JSON语法
- 字符串必须使用双引号
- 对象键必须加引号
- 不能有多余的逗号

#### 示例
```json
{
  "name": "张三",
  "age": 30,
  "skills": ["Python", "JavaScript"],
  "active": true
}
```

### 5. 表格格式

#### 标准表格
```
| 列1 | 列2 | 列3 |
| --- | --- | --- |
| 数据1 | 数据2 | 数据3 |
| 数据4 | 数据5 | 数据6 |
```

### 6. 段落和换行

#### 段落分隔
- 段落之间使用**两个换行符**分隔
- 句子结束后换行使用**一个换行符**

#### 中英文混排
- 中英文之间添加适当空格
- 示例: `这是 English 混排的例子`

## 错误修复规则

### 1. 自动修复的错误类型

#### 数学公式错误
- 缺少美元符号包装
- 不完整的LaTeX语法
- 指数和下标格式错误

#### 代码格式错误
- 缺少代码块结束标记
- 缺少语言标识
- 行内代码缺少反引号

#### Markdown格式错误
- 标题缺少空格
- 列表缺少空格
- 链接格式不完整

#### JSON格式错误
- 缺少引号
- 多余的逗号
- 括号不匹配

### 2. 修复优先级

1. **高优先级**: JSON语法错误、代码块不完整
2. **中优先级**: 数学公式格式、Markdown格式
3. **低优先级**: 空白字符优化、中英文间距

### 3. 修复策略

#### 保守修复
- 只修复明显的语法错误
- 保持原有内容结构
- 最小化改动

#### 渐进式修复
- 根据内容长度和复杂度选择修复强度
- 短文本: 基础清理
- 中等文本: 轻度修复
- 长文本: 完整修复

## 质量检查标准

### 1. 格式一致性检查
- [ ] 数学公式是否正确包装
- [ ] 代码块是否完整
- [ ] Markdown语法是否正确
- [ ] JSON格式是否有效

### 2. 内容完整性检查
- [ ] 修复后内容是否完整
- [ ] 是否有意外丢失的信息
- [ ] 格式修复是否影响语义

### 3. 可读性检查
- [ ] 格式是否清晰易读
- [ ] 空白字符是否适当
- [ ] 段落结构是否合理

## 配置选项

### 格式修复配置
```python
format_rules = {
    'enable_json_repair': True,        # 启用JSON修复
    'enable_math_repair': True,        # 启用数学公式修复
    'enable_markdown_repair': True,    # 启用Markdown修复
    'enable_code_repair': True,        # 启用代码修复
    'enable_structure_repair': True,   # 启用结构修复
    'correction_intensity': 'MEDIUM',  # 修复强度: LIGHT/MEDIUM/STRONG
    'enable_caching': True,           # 启用缓存
    'enable_performance_optimization': True  # 启用性能优化
}
```

## 使用示例

### 修复前的错误输出
```
好的！下面我来详细说明一下欧拉公式：

欧拉公式：e^{ix} = cos x + i sin x

其中，i 是虚数单位，满足 i^2 = -1

--- 

### 从泰勒级数展开证明

我们先回顾一下素级数展开：

- 指数函数的泰勒展开：
e^z = \sum_{n=0}^{\infty} \frac{z^n}{n!}

- 余弦函数的泰勒展开：
cos x = \sum_{n=0}^{\infty} \frac{(-1)^n x^{2n}}{(2n)!}

这里 z 可以是复数。

当 n = 2k 时：
{2k} = (2^k = (-1)^k
```

### 修复后的正确输出
```
好的！下面我来详细说明一下欧拉公式：

**欧拉公式**: $e^{ix} = \cos x + i \sin x$

其中，$i$ 是虚数单位，满足 $i^2 = -1$

---

## 从泰勒级数展开证明

我们先回顾一下级数展开：

- 指数函数的泰勒展开：
$$e^z = \sum_{n=0}^{\infty} \frac{z^n}{n!}$$

- 余弦函数的泰勒展开：
$$\cos x = \sum_{n=0}^{\infty} \frac{(-1)^n x^{2n}}{(2n)!}$$

这里 $z$ 可以是复数。

当 $n = 2k$ 时：
$$\{2k\} = (2)^k = (-1)^k$$
```

## 实现说明

本格式规范通过以下组件实现：

1. **格式检测器**: 自动识别内容类型
2. **修复引擎**: 应用相应的修复规则
3. **质量验证器**: 检查修复结果
4. **缓存系统**: 提高处理效率

## 更新日志

### v1.0 (2025-01-25)
- 初始版本发布
- 定义基础格式规范
- 实现核心修复功能

---

**注意**: 本规范会根据实际使用情况持续更新和完善。