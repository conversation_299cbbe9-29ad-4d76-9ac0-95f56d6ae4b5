<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\res"><file name="ic_foreground_logo" path="C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\res\drawable\ic_foreground_logo.webp" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\res\mipmap-hdpi\ic_launcher_foreground.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="kztalk" path="C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\res\mipmap-hdpi\kztalk.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\res\mipmap-xhdpi\ic_launcher_foreground.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="kztalk" path="C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\res\mipmap-xhdpi\kztalk.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\res\mipmap-xxhdpi\ic_launcher_foreground.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="kztalk" path="C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\res\mipmap-xxhdpi\kztalk.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\res\values\colors.xml" qualifiers=""><color name="purple_200">#FFBB86FC</color><color name="purple_500">#FF6200EE</color><color name="purple_700">#FF3700B3</color><color name="teal_200">#FF03DAC5</color><color name="teal_700">#FF018786</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color></file><file path="C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\res\values\ic_launcher_background.xml" qualifiers=""><color name="ic_launcher_background">#FFFFFF</color></file><file path="C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\res\values\ids.xml" qualifiers=""><item name="webview_template_tag_key" type="id"/></file><file path="C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\res\values\ktalk.xml" qualifiers=""><color name="ktalk">#FFFFFF</color></file><file path="C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">EveryTalk</string><string name="connecting_to_model">正在连接大模型</string><string name="code_copied">代码已复制</string><string name="copy">复制</string><string name="view_sources">查看参考来源 (%d)</string><string name="no_permission_open_link">没有权限打开此链接</string><string name="no_app_found_for_link">没有找到可以打开此链接的应用</string><string name="cannot_open_link">无法打开链接: %s</string><string name="ai_reply_message">AI回复消息</string></file><file path="C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.App1" parent="android:Theme.Material.Light.NoActionBar">
        <item name="android:windowBackground">@android:color/transparent</item>
    </style></file><file name="backup_rules" path="C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="file_paths" path="C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\res\xml\file_paths.xml" qualifiers="" type="xml"/><file name="network_security_config" path="C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\res\xml\network_security_config.xml" qualifiers="" type="xml"/><file name="logo_dark" path="C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\res\drawable\logo_dark.png" qualifiers="" type="drawable"/><file name="logo2" path="C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\res\drawable\logo2.png" qualifiers="" type="drawable"/><file name="logo2" path="C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\res\drawable-night\logo2.png" qualifiers="night-v8" type="drawable"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\res\mipmap-night-anydpi-v26\ic_launcher.xml" qualifiers="night-anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\res\mipmap-night-anydpi-v26\ic_launcher_round.xml" qualifiers="night-anydpi-v26" type="mipmap"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>