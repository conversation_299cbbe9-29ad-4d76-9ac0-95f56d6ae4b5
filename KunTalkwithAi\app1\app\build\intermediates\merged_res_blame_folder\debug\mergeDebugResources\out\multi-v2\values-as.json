{"logs": [{"outputFile": "com.example.everytalk.app-mergeDebugResources-53:/values-as/values-as.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\78e087dd1cdd09d6432d260deb7bf0a8\\transformed\\foundation-release\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,155,240", "endColumns": "99,84,87", "endOffsets": "150,235,323"}, "to": {"startLines": "2,87,88", "startColumns": "4,4,4", "startOffsets": "105,8965,9050", "endColumns": "99,84,87", "endOffsets": "200,9045,9133"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e5d97d34d84112940f02348422c3753d\\transformed\\ui-release\\res\\values-as\\values-as.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "206,301,386,479,577,664,761,860,949,1039,1122,1207,1286,1376,1451,1526,1600,1675,1741", "endColumns": "94,84,92,97,86,96,98,88,89,82,84,78,89,74,74,73,74,65,117", "endOffsets": "296,381,474,572,659,756,855,944,1034,1117,1202,1281,1371,1446,1521,1595,1670,1736,1854"}, "to": {"startLines": "10,11,12,13,14,15,16,74,75,76,77,78,79,80,81,82,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "955,1050,1135,1228,1326,1413,1510,7865,7954,8044,8127,8212,8291,8381,8456,8531,8706,8781,8847", "endColumns": "94,84,92,97,86,96,98,88,89,82,84,78,89,74,74,73,74,65,117", "endOffsets": "1045,1130,1223,1321,1408,1505,1604,7949,8039,8122,8207,8286,8376,8451,8526,8600,8776,8842,8960"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\39e186d1df9a6e64a80555ea95166efd\\transformed\\material3-release\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,294,407,525,621,715,826,970,1091,1233,1318,1416,1511,1610,1726,1854,1957,2088,2218,2347,2527,2647,2765,2889,3022,3118,3214,3335,3461,3558,3668,3776,3912,4056,4166,4268,4345,4446,4547,4653,4744,4836,4945,5025,5110,5211,5316,5414,5516,5603,5710,5809,5913,6034,6114,6217", "endColumns": "118,119,112,117,95,93,110,143,120,141,84,97,94,98,115,127,102,130,129,128,179,119,117,123,132,95,95,120,125,96,109,107,135,143,109,101,76,100,100,105,90,91,108,79,84,100,104,97,101,86,106,98,103,120,79,102,93", "endOffsets": "169,289,402,520,616,710,821,965,1086,1228,1313,1411,1506,1605,1721,1849,1952,2083,2213,2342,2522,2642,2760,2884,3017,3113,3209,3330,3456,3553,3663,3771,3907,4051,4161,4263,4340,4441,4542,4648,4739,4831,4940,5020,5105,5206,5311,5409,5511,5598,5705,5804,5908,6029,6109,6212,6306"}, "to": {"startLines": "17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1609,1728,1848,1961,2079,2175,2269,2380,2524,2645,2787,2872,2970,3065,3164,3280,3408,3511,3642,3772,3901,4081,4201,4319,4443,4576,4672,4768,4889,5015,5112,5222,5330,5466,5610,5720,5822,5899,6000,6101,6207,6298,6390,6499,6579,6664,6765,6870,6968,7070,7157,7264,7363,7467,7588,7668,7771", "endColumns": "118,119,112,117,95,93,110,143,120,141,84,97,94,98,115,127,102,130,129,128,179,119,117,123,132,95,95,120,125,96,109,107,135,143,109,101,76,100,100,105,90,91,108,79,84,100,104,97,101,86,106,98,103,120,79,102,93", "endOffsets": "1723,1843,1956,2074,2170,2264,2375,2519,2640,2782,2867,2965,3060,3159,3275,3403,3506,3637,3767,3896,4076,4196,4314,4438,4571,4667,4763,4884,5010,5107,5217,5325,5461,5605,5715,5817,5894,5995,6096,6202,6293,6385,6494,6574,6659,6760,6865,6963,7065,7152,7259,7358,7462,7583,7663,7766,7860"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e5127fbf4aa18facdfc5d332b39b8229\\transformed\\core-1.16.0\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,156,259,367,472,576,676,805", "endColumns": "100,102,107,104,103,99,128,100", "endOffsets": "151,254,362,467,571,671,800,901"}, "to": {"startLines": "3,4,5,6,7,8,9,83", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "205,306,409,517,622,726,826,8605", "endColumns": "100,102,107,104,103,99,128,100", "endOffsets": "301,404,512,617,721,821,950,8701"}}]}]}