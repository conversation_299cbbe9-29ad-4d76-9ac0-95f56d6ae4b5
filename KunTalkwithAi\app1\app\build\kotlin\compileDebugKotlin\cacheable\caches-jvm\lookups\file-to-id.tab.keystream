   C a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / d a t a / D a t a C l a s s / A p i C o n f i g . k t   H a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / d a t a / D a t a C l a s s / A p i C o n t e n t P a r t . k t   I a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / d a t a / D a t a C l a s s / A p i M e s s a g e T y p e s . k t   E a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / d a t a / D a t a C l a s s / C h a t R e q u e s t . k t   E a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / d a t a / D a t a C l a s s / C o n t e n t P a r t . k t   J a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / d a t a / D a t a C l a s s / G e m i n i A p i R e q u e s t . k t   K a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / d a t a / D a t a C l a s s / G e m i n i A p i R e s p o n s e . k t   R a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / d a t a / D a t a C l a s s / G e n e r a t i o n R e l a t e d C o n f i g s . k t   G a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / d a t a / D a t a C l a s s / G i t h u b R e l e a s e . k t   B a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / d a t a / D a t a C l a s s / I M e s s a g e . k t   A a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / d a t a / D a t a C l a s s / M e s s a g e . k t   F a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / d a t a / D a t a C l a s s / M o d a l i t y T y p e . k t   I a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / d a t a / D a t a C l a s s / W e b S e a r c h R e s u l t . k t   Q a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / d a t a / l o c a l / S h a r e d P r e f e r e n c e s D a t a S o u r c e . k t   E a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / d a t a / n e t w o r k / A n y S e r i a l i z e r . k t   A a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / d a t a / n e t w o r k / A p i C l i e n t . k t   L a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / d a t a / n e t w o r k / A p i M e s s a g e S e r i a l i z e r . k t   F a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / d a t a / n e t w o r k / A p p S t r e a m E v e n t . k t   E a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / m o d e l s / M e d i a S e l e c t i o n T y p e s . k t   < a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / n a v i g a t i o n / S c r e e n . k t   E a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / s t a t e c o n t r o l l e r / A p i H a n d l e r . k t   G a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / s t a t e c o n t r o l l e r / A p p V i e w M o d e l . k t   G a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / s t a t e c o n t r o l l e r / M a i n A c t i v i t y . k t   H a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / s t a t e c o n t r o l l e r / M e s s a g e S e n d e r . k t   O a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / s t a t e c o n t r o l l e r / V i e w M o d e l S t a t e H o l d e r . k t   B a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / u i / c o m p o n e n t s / A p p T o p B a r . k t   D a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / u i / c o m p o n e n t s / C o d e P r e v i e w . k t   E a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / u i / c o m p o n e n t s / M a r k d o w n T e x t . k t   A a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / u i / c o m p o n e n t s / M a t h V i e w . k t   M a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / u i / c o m p o n e n t s / S c r o l l T o B o t t o m B u t t o n . k t   I a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / u i / c o m p o n e n t s / W e b S o u r c e s D i a l o g . k t   H a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / u i / c o m p o n e n t s / W e b V i e w M a t h V i e w . k t   L a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / u i / c o m p o n e n t s / m a t h / C a n v a s M a t h V i e w . k t   U a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / u i / c o m p o n e n t s / m a t h / H i g h P e r f o r m a n c e M a t h V i e w . k t   G a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / u i / c o m p o n e n t s / m a t h / M a t h C a c h e . k t   J a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / u i / c o m p o n e n t s / m a t h / M a t h M L P a r s e r . k t   Q a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / u i / c o m p o n e n t s / m a t h / M a t h P e r f o r m a n c e D e m o . k t   J a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / u i / c o m p o n e n t s / m a t h / M a t h R e n d e r e r . k t   X a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / u i / s c r e e n s / B u b b l e M a i n / M a i n / B u b b l e C o n t e n t T y p e s . k t   P a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / u i / s c r e e n s / B u b b l e M a i n / M a i n / T h i n k i n g U I . k t   Q a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / u i / s c r e e n s / M a i n S c r e e n / A p p D r a w e r C o n t e n t . k t   K a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / u i / s c r e e n s / M a i n S c r e e n / C h a t S c r e e n . k t   Q a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / u i / s c r e e n s / M a i n S c r e e n / c h a t / C h a t D i a l o g s . k t   S a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / u i / s c r e e n s / M a i n S c r e e n / c h a t / C h a t I n p u t A r e a . k t   R a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / u i / s c r e e n s / M a i n S c r e e n / c h a t / C h a t L i s t I t e m . k t   V a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / u i / s c r e e n s / M a i n S c r e e n / c h a t / C h a t M e s s a g e s L i s t . k t   \ a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / u i / s c r e e n s / M a i n S c r e e n / c h a t / C h a t S c r o l l S t a t e M a n a g e r . k t   S a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / u i / s c r e e n s / M a i n S c r e e n / c h a t / E m p t y C h a t V i e w . k t   _ a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / u i / s c r e e n s / M a i n S c r e e n / c h a t / M o d e l S e l e c t i o n B o t t o m S h e e t . k t   W a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / u i / s c r e e n s / M a i n S c r e e n / d r a w e r / D r a w e r C o n s t a n t s . k t   U a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / u i / s c r e e n s / M a i n S c r e e n / d r a w e r / D r a w e r D i a l o g s . k t   V a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / u i / s c r e e n s / M a i n S c r e e n / d r a w e r / D r a w e r I t e m M e n u . k t   V a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / u i / s c r e e n s / M a i n S c r e e n / d r a w e r / D r a w e r L i s t I t e m . k t   T a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / u i / s c r e e n s / M a i n S c r e e n / d r a w e r / D r a w e r M o d e l s . k t   S a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / u i / s c r e e n s / M a i n S c r e e n / d r a w e r / D r a w e r U t i l s . k t   N a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / u i / s c r e e n s / s e t t i n g s / S e t t i n g s D i a l o g s . k t   M a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / u i / s c r e e n s / s e t t i n g s / S e t t i n g s S c r e e n . k t   T a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / u i / s c r e e n s / s e t t i n g s / S e t t i n g s S c r e e n C o n t e n t . k t   L a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / u i / s c r e e n s / s e t t i n g s / S e t t i n g s U t i l s . k t   M a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / u i / s c r e e n s / v i e w m o d e l / C o n f i g M a n a g e r . k t   V a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / u i / s c r e e n s / v i e w m o d e l / D a t a P e r s i s t e n c e M a n a g e r . k t   N a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / u i / s c r e e n s / v i e w m o d e l / H i s t o r y M a n a g e r . k t   > a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / u i / t h e m e / C h a t C o l o r s . k t   9 a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / u i / t h e m e / C o l o r . k t   > a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / u i / t h e m e / D i m e n s i o n s . k t   9 a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / u i / t h e m e / T h e m e . k t   8 a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / u i / t h e m e / T y p e . k t   C a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / u i / u t i l / S c r o l l C o n t r o l l e r . k t   9 a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / u t i l / A p p L o g g e r . k t   C a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / u t i l / A u d i o R e c o r d e r H e l p e r . k t   @ a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / u t i l / B i t m a p S e r i a l i z e r . k t   ? a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / u t i l / C o d e H i g h l i g h t e r . k t   ; a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / u t i l / F i l e M a n a g e r . k t   ; a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / u t i l / I m a g e L o a d e r . k t   @ a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / u t i l / M a r k d o w n T e x t U t i l . k t   B a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / u t i l / P e r f o r m a n c e M o n i t o r . k t   K a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / u t i l / S e l e c t e d M e d i a I t e m S e r i a l i z e r . k t   = a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / u t i l / U r i S e r i a l i z e r . k t   > a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / u t i l / V e r s i o n C h e c k e r . k t   O a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / u t i l / m e s s a g e p r o c e s s o r / E r r o r C o r r e c t o r . k t   P a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / u t i l / m e s s a g e p r o c e s s o r / F o r m a t C o r r e c t o r . k t   Q a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / u t i l / m e s s a g e p r o c e s s o r / M e s s a g e P r o c e s s o r . k t   G a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / u t i l / m e s s a g e p r o c e s s o r / M o d e l s . k t   U a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / u t i l / m e s s a g e p r o c e s s o r / R e a l t i m e P r e p r o c e s s o r . k t   Y a p p / s r c / m a i n / j a v a / c o m / e x a m p l e / e v e r y t a l k / u t i l / m e s s a g e p r o c e s s o r / T h i n k i n g C o n t e n t P r o c e s s o r . k t                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          